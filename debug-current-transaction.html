<!DOCTYPE html>
<html>
<head>
    <title>Debug Current Transaction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .transaction { margin: 15px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; color: red; }
        .success { background: #e8f5e9; color: green; }
        .warning { background: #fff3e0; color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Debug Current Transaction</h1>
    
    <button onclick="debugLatestTransaction()">Debug Latest Transaction</button>
    <button onclick="debugAllAppointmentTransactions()">Debug All Appointment Transactions</button>
    
    <div id="debug-result"></div>

    <script>
        function debugLatestTransaction() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                document.getElementById('debug-result').innerHTML = '<div class="error">No transactions found</div>';
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                if (transactions.length === 0) {
                    document.getElementById('debug-result').innerHTML = '<div class="error">No transactions in storage</div>';
                    return;
                }

                // Get the latest transaction
                const latestTransaction = transactions[transactions.length - 1];
                
                console.log('Latest Transaction:', latestTransaction);
                
                let html = '<h2>Latest Transaction Analysis</h2>';
                
                // Check if it's an appointment transaction
                const isAppointmentTx = latestTransaction.source === 'calendar' || 
                                      (latestTransaction.reference && latestTransaction.reference.type === 'appointment');
                
                if (isAppointmentTx) {
                    const hasDiscount = latestTransaction.discountAmount && latestTransaction.discountAmount > 0;
                    const expectedAmount = hasDiscount ? 
                        (latestTransaction.serviceAmount || 0) + (latestTransaction.productAmount || 0) : 
                        latestTransaction.amount;
                    
                    const amountCorrect = Math.abs(latestTransaction.amount - expectedAmount) < 0.01;
                    
                    html += `
                        <div class="transaction ${amountCorrect ? 'success' : 'error'}">
                            <h3>🔍 Transaction Details</h3>
                            <p><strong>ID:</strong> ${latestTransaction.id}</p>
                            <p><strong>Type:</strong> ${latestTransaction.type}</p>
                            <p><strong>Source:</strong> ${latestTransaction.source}</p>
                            <p><strong>Amount:</strong> ${latestTransaction.amount}</p>
                            <p><strong>Payment Method:</strong> ${latestTransaction.paymentMethod}</p>
                            <p><strong>Description:</strong> ${latestTransaction.description}</p>
                            
                            <h4>💰 Discount Information</h4>
                            <p><strong>Service Amount:</strong> ${latestTransaction.serviceAmount || 'N/A'}</p>
                            <p><strong>Product Amount:</strong> ${latestTransaction.productAmount || 'N/A'}</p>
                            <p><strong>Original Service Amount:</strong> ${latestTransaction.originalServiceAmount || 'N/A'}</p>
                            <p><strong>Discount Percentage:</strong> ${latestTransaction.discountPercentage || 'N/A'}%</p>
                            <p><strong>Discount Amount:</strong> ${latestTransaction.discountAmount || 'N/A'}</p>
                            
                            <h4>✅ Validation</h4>
                            <p><strong>Has Discount:</strong> ${hasDiscount ? 'Yes' : 'No'}</p>
                            <p><strong>Expected Amount:</strong> ${expectedAmount}</p>
                            <p><strong>Amount Correct:</strong> ${amountCorrect ? 'YES ✅' : 'NO ❌'}</p>
                            
                            ${!amountCorrect ? `
                                <div style="color: red; font-weight: bold;">
                                    ⚠️ ISSUE: Transaction amount (${latestTransaction.amount}) doesn't match expected discounted amount (${expectedAmount})
                                </div>
                            ` : ''}
                            
                            <h4>📋 Raw Transaction Data</h4>
                            <pre>${JSON.stringify(latestTransaction, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="transaction warning">
                            <h3>ℹ️ Not an Appointment Transaction</h3>
                            <p>Latest transaction is not from an appointment.</p>
                            <p><strong>Source:</strong> ${latestTransaction.source}</p>
                            <p><strong>Type:</strong> ${latestTransaction.type}</p>
                            <pre>${JSON.stringify(latestTransaction, null, 2)}</pre>
                        </div>
                    `;
                }
                
                document.getElementById('debug-result').innerHTML = html;
                
            } catch (error) {
                document.getElementById('debug-result').innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function debugAllAppointmentTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                document.getElementById('debug-result').innerHTML = '<div class="error">No transactions found</div>';
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                const appointmentTransactions = transactions.filter(tx => 
                    tx.source === 'calendar' || 
                    (tx.reference && tx.reference.type === 'appointment')
                );

                if (appointmentTransactions.length === 0) {
                    document.getElementById('debug-result').innerHTML = '<div class="warning">No appointment transactions found</div>';
                    return;
                }

                let html = '<h2>All Appointment Transactions</h2>';
                let correctCount = 0;
                let incorrectCount = 0;

                appointmentTransactions.forEach((tx, index) => {
                    const hasDiscount = tx.discountAmount && tx.discountAmount > 0;
                    const expectedAmount = hasDiscount ? 
                        (tx.serviceAmount || 0) + (tx.productAmount || 0) : 
                        tx.amount;
                    
                    const amountCorrect = Math.abs(tx.amount - expectedAmount) < 0.01;
                    
                    if (amountCorrect) correctCount++;
                    else incorrectCount++;

                    const statusClass = amountCorrect ? 'success' : 'error';
                    const statusIcon = amountCorrect ? '✅' : '❌';

                    html += `
                        <div class="transaction ${statusClass}">
                            <h4>${statusIcon} Transaction ${index + 1}</h4>
                            <p><strong>ID:</strong> ${tx.id}</p>
                            <p><strong>Amount:</strong> ${tx.amount} (Expected: ${expectedAmount})</p>
                            <p><strong>Payment Method:</strong> ${tx.paymentMethod}</p>
                            <p><strong>Discount:</strong> ${hasDiscount ? `${tx.discountPercentage}% (-${tx.discountAmount})` : 'None'}</p>
                            <p><strong>Service/Product:</strong> ${tx.serviceAmount || 0} / ${tx.productAmount || 0}</p>
                            <p><strong>Description:</strong> ${tx.description}</p>
                            ${!amountCorrect ? '<p style="color: red;"><strong>⚠️ Amount mismatch detected!</strong></p>' : ''}
                        </div>
                    `;
                });

                html += `
                    <div class="transaction">
                        <h3>📊 Summary</h3>
                        <p><strong>Total Appointment Transactions:</strong> ${appointmentTransactions.length}</p>
                        <p><strong>✅ Correct:</strong> ${correctCount}</p>
                        <p><strong>❌ Incorrect:</strong> ${incorrectCount}</p>
                        <p><strong>Status:</strong> ${incorrectCount === 0 ? 'All good!' : `${incorrectCount} need fixing`}</p>
                    </div>
                `;

                document.getElementById('debug-result').innerHTML = html;

            } catch (error) {
                document.getElementById('debug-result').innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Auto-run on page load
        window.onload = debugLatestTransaction;
    </script>
</body>
</html>
