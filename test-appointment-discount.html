<!DOCTYPE html>
<html>
<head>
    <title>Test Appointment Discount Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: red; }
        .success { background: #e8f5e9; color: green; }
    </style>
</head>
<body>
    <h1>Test Appointment Discount Issue</h1>
    
    <div class="test-section">
        <h2>Current Transactions Analysis</h2>
        <button onclick="analyzeTransactions()">Analyze Current Transactions</button>
        <div id="analysis-result"></div>
    </div>

    <div class="test-section">
        <h2>Simulate Appointment Payment Flow</h2>
        <button onclick="simulateAppointmentPayment()">Simulate Payment with 10% Discount</button>
        <div id="simulation-result"></div>
    </div>

    <script>
        function analyzeTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                document.getElementById('analysis-result').innerHTML = '<div class="error">No transactions found</div>';
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let html = '<h3>Transaction Analysis:</h3>';
                
                transactions.forEach((tx, index) => {
                    if (tx.source === 'calendar' || tx.type === 'consolidated_sale') {
                        const hasDiscount = tx.discountAmount && tx.discountAmount > 0;
                        const expectedAmount = hasDiscount ? (tx.serviceAmount || 0) + (tx.productAmount || 0) : tx.amount;
                        const amountCorrect = Math.abs(tx.amount - expectedAmount) < 0.01;
                        
                        html += `
                            <div class="result ${amountCorrect ? 'success' : 'error'}">
                                <strong>Transaction ${index + 1}: ${tx.id}</strong><br>
                                Source: ${tx.source}<br>
                                Type: ${tx.type}<br>
                                Amount: ${tx.amount}<br>
                                Service Amount: ${tx.serviceAmount || 'N/A'}<br>
                                Product Amount: ${tx.productAmount || 'N/A'}<br>
                                Original Service Amount: ${tx.originalServiceAmount || 'N/A'}<br>
                                Discount %: ${tx.discountPercentage || 'N/A'}<br>
                                Discount Amount: ${tx.discountAmount || 'N/A'}<br>
                                Payment Method: ${tx.paymentMethod}<br>
                                Expected Amount: ${expectedAmount}<br>
                                Amount Correct: ${amountCorrect ? 'YES' : 'NO'}<br>
                                Description: ${tx.description}
                            </div>
                        `;
                    }
                });
                
                document.getElementById('analysis-result').innerHTML = html;
            } catch (error) {
                document.getElementById('analysis-result').innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function simulateAppointmentPayment() {
            console.log('🧪 Simulating appointment payment flow...');
            
            // Step 1: Mock appointment data
            const mockAppointment = {
                id: 'test-appointment-' + Date.now(),
                service: 'Hair Cut',
                price: 100,
                clientName: 'Test Client',
                clientId: 'test-client',
                location: 'loc1'
            };

            // Step 2: Simulate payment dialog completion with 10% discount
            const discountPercentage = 10;
            const discountAmount = 10; // 10% of 100
            const originalAmount = 100;
            const finalAmount = 90;

            console.log('💰 Payment Dialog Parameters:', {
                discountPercentage,
                discountAmount,
                originalAmount,
                finalAmount
            });

            // Step 3: Simulate handlePaymentComplete logic
            const updatedAppointment = {
                ...mockAppointment,
                status: 'completed',
                paymentStatus: 'paid',
                paymentMethod: 'Credit Card',
                paymentDate: new Date().toISOString(),
                discountPercentage: discountPercentage,
                discountAmount: discountAmount,
                originalAmount: originalAmount,
                finalAmount: finalAmount
            };

            console.log('📋 Updated Appointment:', updatedAppointment);

            // Step 4: Simulate ConsolidatedTransactionService call
            console.log('🔧 Calling ConsolidatedTransactionService with:', {
                appointment: updatedAppointment,
                paymentMethod: 'credit_card',
                discountPercentage: updatedAppointment.discountPercentage,
                discountAmount: updatedAppointment.discountAmount
            });

            // Mock the service logic
            const serviceItem = {
                id: 'service-hair-cut',
                name: 'Hair Cut',
                quantity: 1,
                unitPrice: 100,
                totalPrice: 100,
                type: 'service',
                discountApplied: false,
                discountPercentage: 0,
                discountAmount: 0,
                originalPrice: 100
            };

            // Apply discount logic
            if (discountPercentage && discountPercentage > 0) {
                const itemDiscountAmount = (100 * discountPercentage) / 100;
                serviceItem.discountApplied = true;
                serviceItem.discountPercentage = discountPercentage;
                serviceItem.discountAmount = itemDiscountAmount;
                serviceItem.totalPrice = 100 - itemDiscountAmount;

                console.log('💸 Discount Applied:', {
                    originalPrice: 100,
                    discountPercentage,
                    itemDiscountAmount,
                    finalPrice: serviceItem.totalPrice
                });
            }

            const serviceAmount = serviceItem.totalPrice;
            const productAmount = 0;
            const totalAmount = serviceAmount + productAmount;

            console.log('📊 Final Calculation:', {
                serviceAmount,
                productAmount,
                totalAmount,
                shouldBe90: totalAmount === 90
            });

            const result = `
                <h3>Simulation Results:</h3>
                <div class="result ${totalAmount === 90 ? 'success' : 'error'}">
                    <strong>Expected Behavior:</strong><br>
                    Original Amount: $100<br>
                    Discount: 10% ($10)<br>
                    Final Amount: $90<br><br>
                    
                    <strong>Actual Calculation:</strong><br>
                    Service Amount: $${serviceAmount}<br>
                    Product Amount: $${productAmount}<br>
                    Total Amount: $${totalAmount}<br><br>
                    
                    <strong>Result:</strong> ${totalAmount === 90 ? 'CORRECT ✅' : 'INCORRECT ❌'}<br>
                    
                    ${totalAmount !== 90 ? '<br><strong>Issue:</strong> The discount is not being applied correctly in the transaction amount calculation.' : ''}
                </div>
            `;

            document.getElementById('simulation-result').innerHTML = result;
        }

        // Auto-run analysis on page load
        window.onload = analyzeTransactions;
    </script>
</body>
</html>
