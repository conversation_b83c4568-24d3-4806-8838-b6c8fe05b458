<!DOCTYPE html>
<html>
<head>
    <title>Debug Transactions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .transaction { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .discount-info { background: #e8f5e9; padding: 5px; margin: 5px 0; }
        .original-amount { text-decoration: line-through; color: #666; }
        .discounted-amount { color: #2e7d32; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Transaction Debug Tool</h1>
    <button onclick="loadTransactions()">Load Transactions</button>
    <button onclick="clearTransactions()">Clear All Transactions</button>
    <button onclick="fixDiscountedTransactions()">Fix Discounted Transactions</button>
    
    <div id="output"></div>

    <script>
        function loadTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            const output = document.getElementById('output');
            
            if (!stored) {
                output.innerHTML = '<p>No transactions found in localStorage</p>';
                return;
            }
            
            try {
                const transactions = JSON.parse(stored);
                console.log('Loaded transactions:', transactions);
                
                let html = `<h2>Found ${transactions.length} transactions</h2>`;
                
                transactions.forEach((tx, index) => {
                    html += `<div class="transaction">
                        <h3>Transaction ${index + 1}: ${tx.id}</h3>
                        <p><strong>Type:</strong> ${tx.type}</p>
                        <p><strong>Amount:</strong> ${tx.amount}</p>
                        <p><strong>Service Amount:</strong> ${tx.serviceAmount || 'N/A'}</p>
                        <p><strong>Product Amount:</strong> ${tx.productAmount || 'N/A'}</p>
                        <p><strong>Original Service Amount:</strong> ${tx.originalServiceAmount || 'N/A'}</p>
                        <p><strong>Discount Percentage:</strong> ${tx.discountPercentage || 'N/A'}</p>
                        <p><strong>Discount Amount:</strong> ${tx.discountAmount || 'N/A'}</p>
                        <p><strong>Description:</strong> ${tx.description}</p>
                        <p><strong>Client:</strong> ${tx.clientName || 'N/A'}</p>
                        <p><strong>Date:</strong> ${tx.date}</p>
                        <p><strong>Source:</strong> ${tx.source}</p>
                        ${tx.metadata ? `<p><strong>Metadata:</strong> ${JSON.stringify(tx.metadata, null, 2)}</p>` : ''}
                    </div>`;
                });
                
                output.innerHTML = html;
            } catch (error) {
                output.innerHTML = `<p>Error parsing transactions: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
        
        function clearTransactions() {
            if (confirm('Are you sure you want to clear all transactions? This cannot be undone.')) {
                localStorage.removeItem('vanity_transactions');
                document.getElementById('output').innerHTML = '<p>All transactions cleared</p>';
            }
        }
        
        function fixDiscountedTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                alert('No transactions found');
                return;
            }
            
            try {
                const transactions = JSON.parse(stored);
                let fixedCount = 0;
                
                transactions.forEach(tx => {
                    // Check if this is a consolidated transaction with discount info but wrong amount
                    if (tx.type === 'consolidated_sale' && tx.discountAmount && tx.discountAmount > 0) {
                        const expectedAmount = (tx.serviceAmount || 0) + (tx.productAmount || 0);
                        
                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            console.log(`Fixing transaction ${tx.id}: ${tx.amount} -> ${expectedAmount}`);
                            tx.amount = expectedAmount;
                            fixedCount++;
                        }
                    }
                });
                
                if (fixedCount > 0) {
                    localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
                    alert(`Fixed ${fixedCount} transactions. Please refresh the accounting page.`);
                } else {
                    alert('No transactions needed fixing');
                }
                
                loadTransactions(); // Reload to show changes
            } catch (error) {
                alert(`Error fixing transactions: ${error.message}`);
            }
        }
        
        // Auto-load on page load
        window.onload = loadTransactions;
    </script>
</body>
</html>
