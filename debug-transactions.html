<!DOCTYPE html>
<html>
<head>
    <title>Debug Transactions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .transaction { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .discount-info { background: #e8f5e9; padding: 5px; margin: 5px 0; }
        .original-amount { text-decoration: line-through; color: #666; }
        .discounted-amount { color: #2e7d32; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Transaction Debug Tool</h1>
    <button onclick="loadTransactions()">Load Transactions</button>
    <button onclick="clearTransactions()">Clear All Transactions</button>
    <button onclick="fixDiscountedTransactions()">Fix All Transaction Issues</button>
    <button onclick="fixPaymentMethods()">Fix Payment Methods Only</button>
    <button onclick="validateTransactions()">Validate Transaction Data</button>
    
    <div id="output"></div>

    <script>
        function loadTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            const output = document.getElementById('output');
            
            if (!stored) {
                output.innerHTML = '<p>No transactions found in localStorage</p>';
                return;
            }
            
            try {
                const transactions = JSON.parse(stored);
                console.log('Loaded transactions:', transactions);
                
                let html = `<h2>Found ${transactions.length} transactions</h2>`;
                
                transactions.forEach((tx, index) => {
                    // Check for issues
                    let issues = [];

                    // Amount consistency check
                    if (tx.type === 'consolidated_sale' && tx.serviceAmount !== undefined && tx.productAmount !== undefined) {
                        const expectedAmount = tx.serviceAmount + tx.productAmount;
                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            issues.push(`Amount mismatch: recorded ${tx.amount}, expected ${expectedAmount}`);
                        }
                    }

                    // Payment method check
                    if (tx.paymentMethod === 'cash' && tx.description &&
                        (tx.description.toLowerCase().includes('mobile') || tx.description.toLowerCase().includes('card'))) {
                        issues.push(`Payment method may be wrong: recorded as cash but description suggests mobile/card`);
                    }

                    const issueClass = issues.length > 0 ? ' style="border-color: red; background-color: #ffebee;"' : '';

                    html += `<div class="transaction"${issueClass}>
                        <h3>Transaction ${index + 1}: ${tx.id} ${issues.length > 0 ? '⚠️' : '✅'}</h3>
                        ${issues.length > 0 ? `<div style="color: red; font-weight: bold;">Issues: ${issues.join('; ')}</div>` : ''}
                        <p><strong>Type:</strong> ${tx.type}</p>
                        <p><strong>Amount:</strong> ${tx.amount}</p>
                        <p><strong>Payment Method:</strong> <span style="color: ${tx.paymentMethod === 'cash' ? 'orange' : 'green'};">${tx.paymentMethod}</span></p>
                        <p><strong>Service Amount:</strong> ${tx.serviceAmount || 'N/A'}</p>
                        <p><strong>Product Amount:</strong> ${tx.productAmount || 'N/A'}</p>
                        <p><strong>Original Service Amount:</strong> ${tx.originalServiceAmount || 'N/A'}</p>
                        <p><strong>Discount Percentage:</strong> ${tx.discountPercentage || 'N/A'}</p>
                        <p><strong>Discount Amount:</strong> ${tx.discountAmount || 'N/A'}</p>
                        <p><strong>Description:</strong> ${tx.description}</p>
                        <p><strong>Client:</strong> ${tx.clientName || 'N/A'}</p>
                        <p><strong>Date:</strong> ${tx.date}</p>
                        <p><strong>Source:</strong> ${tx.source}</p>
                        ${tx.metadata ? `<p><strong>Metadata:</strong> <pre style="font-size: 12px; background: #f5f5f5; padding: 5px;">${JSON.stringify(tx.metadata, null, 2)}</pre></p>` : ''}
                    </div>`;
                });
                
                output.innerHTML = html;
            } catch (error) {
                output.innerHTML = `<p>Error parsing transactions: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
        
        function clearTransactions() {
            if (confirm('Are you sure you want to clear all transactions? This cannot be undone.')) {
                localStorage.removeItem('vanity_transactions');
                document.getElementById('output').innerHTML = '<p>All transactions cleared</p>';
            }
        }
        
        function fixDiscountedTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                alert('No transactions found');
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let fixedAmountCount = 0;
                let fixedPaymentMethodCount = 0;

                transactions.forEach(tx => {
                    // Fix 1: Check if this is a consolidated transaction with discount info but wrong amount
                    if (tx.type === 'consolidated_sale' && tx.discountAmount && tx.discountAmount > 0) {
                        const expectedAmount = (tx.serviceAmount || 0) + (tx.productAmount || 0);

                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            console.log(`Fixing transaction amount ${tx.id}: ${tx.amount} -> ${expectedAmount}`);
                            tx.amount = expectedAmount;
                            fixedAmountCount++;
                        }
                    }

                    // Fix 2: Check for incorrect payment methods (cash when it should be mobile_payment)
                    if (tx.paymentMethod === 'cash' && tx.description && tx.description.toLowerCase().includes('mobile')) {
                        console.log(`Fixing payment method for ${tx.id}: cash -> mobile_payment`);
                        tx.paymentMethod = 'mobile_payment';
                        fixedPaymentMethodCount++;
                    }

                    // Fix 3: Check for transactions that should have discount fields but don't
                    if (tx.type === 'consolidated_sale' && !tx.serviceAmount && !tx.productAmount) {
                        // Try to reconstruct the amounts from the original data
                        if (tx.metadata && tx.metadata.originalTotal && tx.metadata.finalTotal) {
                            const originalTotal = tx.metadata.originalTotal;
                            const finalTotal = tx.metadata.finalTotal;
                            const discountAmount = originalTotal - finalTotal;

                            if (discountAmount > 0) {
                                console.log(`Reconstructing discount fields for ${tx.id}`);
                                tx.originalServiceAmount = originalTotal;
                                tx.serviceAmount = finalTotal;
                                tx.productAmount = 0;
                                tx.discountAmount = discountAmount;
                                tx.discountPercentage = (discountAmount / originalTotal) * 100;
                                tx.amount = finalTotal;
                                fixedAmountCount++;
                            }
                        }
                    }
                });

                if (fixedAmountCount > 0 || fixedPaymentMethodCount > 0) {
                    localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
                    alert(`Fixed ${fixedAmountCount} transaction amounts and ${fixedPaymentMethodCount} payment methods. Please refresh the accounting page.`);
                } else {
                    alert('No transactions needed fixing');
                }

                loadTransactions(); // Reload to show changes
            } catch (error) {
                alert(`Error fixing transactions: ${error.message}`);
            }
        }
        
        function fixPaymentMethods() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                alert('No transactions found');
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let fixedCount = 0;

                transactions.forEach(tx => {
                    // Fix payment methods based on description or metadata
                    if (tx.paymentMethod === 'cash') {
                        if (tx.description && tx.description.toLowerCase().includes('mobile')) {
                            tx.paymentMethod = 'mobile_payment';
                            fixedCount++;
                        } else if (tx.description && tx.description.toLowerCase().includes('card')) {
                            tx.paymentMethod = 'credit_card';
                            fixedCount++;
                        }
                    }
                });

                if (fixedCount > 0) {
                    localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
                    alert(`Fixed ${fixedCount} payment methods. Please refresh the accounting page.`);
                } else {
                    alert('No payment methods needed fixing');
                }

                loadTransactions();
            } catch (error) {
                alert(`Error fixing payment methods: ${error.message}`);
            }
        }

        function validateTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                alert('No transactions found');
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let issues = [];

                transactions.forEach((tx, index) => {
                    // Check for amount consistency
                    if (tx.type === 'consolidated_sale' && tx.serviceAmount !== undefined && tx.productAmount !== undefined) {
                        const expectedAmount = tx.serviceAmount + tx.productAmount;
                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            issues.push(`Transaction ${index + 1} (${tx.id}): Amount mismatch - recorded: ${tx.amount}, expected: ${expectedAmount}`);
                        }
                    }

                    // Check for payment method issues
                    if (tx.paymentMethod === 'cash' && tx.description &&
                        (tx.description.toLowerCase().includes('mobile') || tx.description.toLowerCase().includes('card'))) {
                        issues.push(`Transaction ${index + 1} (${tx.id}): Payment method may be incorrect - recorded as cash but description suggests: ${tx.description}`);
                    }

                    // Check for missing discount fields
                    if (tx.type === 'consolidated_sale' && tx.discountAmount > 0 && !tx.serviceAmount) {
                        issues.push(`Transaction ${index + 1} (${tx.id}): Missing service/product amount breakdown despite having discount`);
                    }
                });

                if (issues.length > 0) {
                    console.log('Transaction validation issues:', issues);
                    alert(`Found ${issues.length} issues:\n\n${issues.slice(0, 5).join('\n')}\n\n${issues.length > 5 ? '... and more. Check console for full list.' : ''}`);
                } else {
                    alert('All transactions look good!');
                }
            } catch (error) {
                alert(`Error validating transactions: ${error.message}`);
            }
        }

        // Auto-load on page load
        window.onload = loadTransactions;
    </script>
</body>
</html>
