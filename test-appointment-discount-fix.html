<!DOCTYPE html>
<html>
<head>
    <title>Test Appointment Discount Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: red; }
        .success { background: #e8f5e9; color: green; }
        .warning { background: #fff3e0; color: orange; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Appointment Discount Fix</h1>
    
    <div class="test-section">
        <h2>Before Fix vs After Fix Comparison</h2>
        <button onclick="testDiscountFlow()">Test Discount Flow</button>
        <div id="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Current Transaction Analysis</h2>
        <button onclick="analyzeCurrentTransactions()">Analyze Current Transactions</button>
        <div id="analysis-result"></div>
    </div>

    <div class="test-section">
        <h2>Fix Existing Transactions</h2>
        <button onclick="fixExistingTransactions()">Fix Existing Appointment Transactions</button>
        <div id="fix-result"></div>
    </div>

    <script>
        function testDiscountFlow() {
            console.log('🧪 Testing Appointment Discount Flow...');
            
            // Simulate the FIXED flow
            const mockAppointment = {
                id: 'test-appointment-' + Date.now(),
                service: 'Hair Cut',
                price: 100,
                clientName: 'Test Client',
                clientId: 'test-client',
                location: 'loc1'
            };

            // Payment dialog parameters (what payment dialog sends)
            const paymentDialogParams = {
                paymentMethod: 'Credit Card',
                giftCardCode: undefined,
                giftCardAmount: undefined,
                discountPercentage: 10,  // 10%
                discountAmount: 10       // $10 discount on $100 service
            };

            console.log('💰 Payment Dialog sends:', paymentDialogParams);

            // handlePaymentComplete logic (FIXED)
            const originalAmount = 100;
            const finalAmount = paymentDialogParams.discountAmount ? 
                originalAmount - paymentDialogParams.discountAmount : originalAmount;

            const updatedAppointment = {
                ...mockAppointment,
                status: 'completed',
                paymentStatus: 'paid',
                paymentMethod: paymentDialogParams.paymentMethod,
                paymentDate: new Date().toISOString(),
                discountPercentage: paymentDialogParams.discountPercentage,
                discountAmount: paymentDialogParams.discountAmount,
                originalAmount: originalAmount,
                finalAmount: finalAmount
            };

            console.log('📋 Updated Appointment:', {
                originalAmount: updatedAppointment.originalAmount,
                finalAmount: updatedAppointment.finalAmount,
                discountPercentage: updatedAppointment.discountPercentage,
                discountAmount: updatedAppointment.discountAmount
            });

            // ConsolidatedTransactionService.createConsolidatedTransaction (FIXED)
            // Now uses original payment dialog parameters, not updatedAppointment values
            const serviceItem = {
                id: 'service-hair-cut',
                name: 'Hair Cut',
                quantity: 1,
                unitPrice: 100,
                totalPrice: 100,
                type: 'service',
                discountApplied: false,
                discountPercentage: 0,
                discountAmount: 0,
                originalPrice: 100
            };

            // Apply discount using ORIGINAL parameters from payment dialog
            const discountPercentage = paymentDialogParams.discountPercentage;
            const discountAmount = paymentDialogParams.discountAmount;

            if (discountPercentage && discountPercentage > 0) {
                const itemDiscountAmount = (100 * discountPercentage) / 100;
                serviceItem.discountApplied = true;
                serviceItem.discountPercentage = discountPercentage;
                serviceItem.discountAmount = itemDiscountAmount;
                serviceItem.totalPrice = 100 - itemDiscountAmount;

                console.log('💸 Discount Applied (FIXED):', {
                    originalPrice: 100,
                    discountPercentage,
                    itemDiscountAmount,
                    finalPrice: serviceItem.totalPrice
                });
            }

            const serviceAmount = serviceItem.totalPrice;
            const productAmount = 0;
            const totalAmount = serviceAmount + productAmount;

            console.log('📊 Final Transaction Amount:', {
                serviceAmount,
                productAmount,
                totalAmount,
                expectedAmount: 90,
                isCorrect: totalAmount === 90
            });

            const resultClass = totalAmount === 90 ? 'success' : 'error';
            const resultText = totalAmount === 90 ? 'FIXED ✅' : 'STILL BROKEN ❌';

            document.getElementById('test-result').innerHTML = `
                <div class="result ${resultClass}">
                    <h3>Discount Flow Test Result: ${resultText}</h3>
                    <p><strong>Test Scenario:</strong> $100 service with 10% discount</p>
                    <p><strong>Expected Transaction Amount:</strong> $90</p>
                    <p><strong>Actual Transaction Amount:</strong> $${totalAmount}</p>
                    <p><strong>Service Amount:</strong> $${serviceAmount}</p>
                    <p><strong>Product Amount:</strong> $${productAmount}</p>
                    <p><strong>Discount Applied:</strong> ${serviceItem.discountApplied ? 'Yes' : 'No'}</p>
                    <p><strong>Discount Amount:</strong> $${serviceItem.discountAmount}</p>
                    
                    ${totalAmount === 90 ? 
                        '<p style="color: green;"><strong>✅ Fix Successful!</strong> Appointment payments now record discounted amounts correctly.</p>' :
                        '<p style="color: red;"><strong>❌ Fix Failed!</strong> There may be additional issues to resolve.</p>'
                    }
                </div>
            `;
        }

        function analyzeCurrentTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                document.getElementById('analysis-result').innerHTML = '<div class="error">No transactions found</div>';
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let appointmentTransactions = transactions.filter(tx => 
                    tx.source === 'calendar' || 
                    (tx.reference && tx.reference.type === 'appointment')
                );

                if (appointmentTransactions.length === 0) {
                    document.getElementById('analysis-result').innerHTML = '<div class="warning">No appointment transactions found</div>';
                    return;
                }

                let html = '<h3>Appointment Transaction Analysis:</h3>';
                let correctCount = 0;
                let incorrectCount = 0;

                appointmentTransactions.forEach((tx, index) => {
                    const hasDiscount = tx.discountAmount && tx.discountAmount > 0;
                    let isCorrect = true;
                    let issues = [];

                    if (hasDiscount) {
                        const expectedAmount = (tx.serviceAmount || 0) + (tx.productAmount || 0);
                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            isCorrect = false;
                            issues.push(`Amount should be ${expectedAmount}, but is ${tx.amount}`);
                        }
                    }

                    if (isCorrect) correctCount++;
                    else incorrectCount++;

                    const statusClass = isCorrect ? 'success' : 'error';
                    const statusIcon = isCorrect ? '✅' : '❌';

                    html += `
                        <div class="result ${statusClass}">
                            <strong>${statusIcon} Transaction ${index + 1}: ${tx.id}</strong><br>
                            Amount: $${tx.amount}<br>
                            Service Amount: $${tx.serviceAmount || 'N/A'}<br>
                            Product Amount: $${tx.productAmount || 'N/A'}<br>
                            Discount: ${hasDiscount ? `${tx.discountPercentage}% (-$${tx.discountAmount})` : 'None'}<br>
                            Payment Method: ${tx.paymentMethod}<br>
                            ${issues.length > 0 ? `<span style="color: red;">Issues: ${issues.join(', ')}</span><br>` : ''}
                            Description: ${tx.description}
                        </div>
                    `;
                });

                html += `
                    <div class="result">
                        <h4>Summary:</h4>
                        <p>✅ Correct: ${correctCount}</p>
                        <p>❌ Incorrect: ${incorrectCount}</p>
                        <p><strong>Status:</strong> ${incorrectCount === 0 ? 'All transactions are correct!' : `${incorrectCount} transactions need fixing`}</p>
                    </div>
                `;

                document.getElementById('analysis-result').innerHTML = html;
            } catch (error) {
                document.getElementById('analysis-result').innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function fixExistingTransactions() {
            const stored = localStorage.getItem('vanity_transactions');
            if (!stored) {
                document.getElementById('fix-result').innerHTML = '<div class="error">No transactions found</div>';
                return;
            }

            try {
                const transactions = JSON.parse(stored);
                let fixedCount = 0;

                transactions.forEach(tx => {
                    // Fix appointment transactions with discount amount issues
                    if ((tx.source === 'calendar' || (tx.reference && tx.reference.type === 'appointment')) &&
                        tx.type === 'consolidated_sale' && 
                        tx.discountAmount && tx.discountAmount > 0) {
                        
                        const expectedAmount = (tx.serviceAmount || 0) + (tx.productAmount || 0);
                        if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                            console.log(`Fixing appointment transaction ${tx.id}: ${tx.amount} -> ${expectedAmount}`);
                            tx.amount = expectedAmount;
                            fixedCount++;
                        }
                    }
                });

                if (fixedCount > 0) {
                    localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
                    document.getElementById('fix-result').innerHTML = `
                        <div class="result success">
                            <h4>✅ Fix Applied Successfully!</h4>
                            <p>Fixed ${fixedCount} appointment transactions.</p>
                            <p>Please refresh the accounting page to see the corrected amounts.</p>
                        </div>
                    `;
                } else {
                    document.getElementById('fix-result').innerHTML = `
                        <div class="result success">
                            <h4>✅ No Fixes Needed</h4>
                            <p>All appointment transactions are already correct.</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('fix-result').innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Auto-run analysis on page load
        window.onload = () => {
            testDiscountFlow();
            analyzeCurrentTransactions();
        };
    </script>
</body>
</html>
