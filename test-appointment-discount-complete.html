<!DOCTYPE html>
<html>
<head>
    <title>Complete Appointment Discount Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step h3 { margin-top: 0; color: #333; }
        .result { background: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #e8f5e9; color: green; }
        .error { background: #ffebee; color: red; }
        .warning { background: #fff3e0; color: orange; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; border-radius: 3px; }
        .highlight { background: yellow; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🧪 Complete Appointment Discount Test</h1>
    <p>This test will verify the entire appointment discount flow from payment to accounting display.</p>

    <div class="step">
        <h3>Step 1: Clear All Transactions</h3>
        <button onclick="clearTransactions()">Clear All Transactions</button>
        <div id="clear-result"></div>
    </div>

    <div class="step">
        <h3>Step 2: Simulate Appointment Payment with Discount</h3>
        <p>This simulates the exact flow that happens when completing an appointment payment with discount.</p>
        <button onclick="simulateAppointmentPayment()">Simulate Appointment Payment (QAR 100 → QAR 90)</button>
        <div id="simulate-result"></div>
    </div>

    <div class="step">
        <h3>Step 3: Verify Transaction Data</h3>
        <button onclick="verifyTransactionData()">Verify Transaction Structure</button>
        <div id="verify-result"></div>
    </div>

    <div class="step">
        <h3>Step 4: Test Accounting Display Logic</h3>
        <button onclick="testAccountingDisplay()">Test How Accounting Page Would Display This</button>
        <div id="display-result"></div>
    </div>

    <div class="step">
        <h3>Step 5: Instructions for Manual Testing</h3>
        <div class="result">
            <h4>📋 Manual Testing Steps:</h4>
            <ol>
                <li>Click "Clear All Transactions" above</li>
                <li>Click "Simulate Appointment Payment" above</li>
                <li>Go to <a href="http://localhost:3000/dashboard/accounting" target="_blank">Accounting Page</a></li>
                <li>Check if the transaction shows:
                    <ul>
                        <li><span class="highlight">QAR 90.00</span> as the final amount (in green)</li>
                        <li><span class="highlight">QAR 100.00</span> crossed out as original amount</li>
                        <li><span class="highlight">10% off</span> discount indicator</li>
                        <li><span class="highlight">Mobile Payment</span> as payment method</li>
                    </ul>
                </li>
                <li>If it shows QAR 100.00 instead of QAR 90.00, the issue is confirmed</li>
            </ol>
        </div>
    </div>

    <script>
        function clearTransactions() {
            try {
                localStorage.removeItem('vanity_transactions');
                document.getElementById('clear-result').innerHTML = `
                    <div class="result success">
                        ✅ All transactions cleared successfully!
                        <br>You can now test with a clean slate.
                    </div>
                `;
                console.log('✅ Transactions cleared');
            } catch (error) {
                document.getElementById('clear-result').innerHTML = `
                    <div class="result error">❌ Error clearing transactions: ${error.message}</div>
                `;
            }
        }

        function simulateAppointmentPayment() {
            try {
                console.log('🧪 Starting appointment payment simulation...');

                // Step 1: Create mock appointment (simulates appointment data)
                const mockAppointment = {
                    id: 'test-appointment-' + Date.now(),
                    service: 'Hair Cut',
                    price: 100,
                    clientId: 'client-123',
                    clientName: 'Test Client',
                    staffId: 'staff-456',
                    staffName: 'Test Stylist',
                    location: 'loc1',
                    date: new Date().toISOString(),
                    status: 'completed',
                    paymentStatus: 'paid',
                    paymentMethod: 'Mobile Payment'
                };

                // Step 2: Payment dialog parameters (what user enters)
                const paymentDialogParams = {
                    paymentMethod: 'Mobile Payment',
                    discountPercentage: 10,
                    discountAmount: 10
                };

                console.log('💳 Payment Dialog Parameters:', paymentDialogParams);

                // Step 3: Enhanced dialog creates updated appointment
                const originalAmount = 100;
                const finalAmount = originalAmount - paymentDialogParams.discountAmount;

                const updatedAppointment = {
                    ...mockAppointment,
                    discountPercentage: paymentDialogParams.discountPercentage,
                    discountAmount: paymentDialogParams.discountAmount,
                    originalAmount: originalAmount,
                    finalAmount: finalAmount
                };

                console.log('📋 Updated Appointment:', updatedAppointment);

                // Step 4: ConsolidatedTransactionService.createConsolidatedTransaction
                // (This simulates the FIXED logic)
                const transactionId = 'TXN-' + Date.now().toString().slice(-8);
                
                // Create service item with discount applied
                const serviceItem = {
                    id: 'service-hair-cut',
                    name: 'Hair Cut',
                    quantity: 1,
                    unitPrice: 100,
                    totalPrice: 100,
                    type: 'service',
                    discountApplied: false,
                    discountPercentage: 0,
                    discountAmount: 0,
                    originalPrice: 100
                };

                // Apply discount using ORIGINAL payment dialog parameters
                const discountPercentage = paymentDialogParams.discountPercentage;
                const discountAmount = paymentDialogParams.discountAmount;

                if (discountPercentage && discountPercentage > 0) {
                    const itemDiscountAmount = (100 * discountPercentage) / 100;
                    serviceItem.discountApplied = true;
                    serviceItem.discountPercentage = discountPercentage;
                    serviceItem.discountAmount = itemDiscountAmount;
                    serviceItem.totalPrice = 100 - itemDiscountAmount;
                }

                const serviceAmount = serviceItem.totalPrice;
                const productAmount = 0;
                const originalServiceAmount = 100;
                const totalAmount = serviceAmount + productAmount;

                // Create transaction object (matches ConsolidatedTransactionService structure)
                const transaction = {
                    id: transactionId,
                    date: new Date(),
                    clientId: updatedAppointment.clientId,
                    clientName: updatedAppointment.clientName,
                    staffId: updatedAppointment.staffId,
                    staffName: updatedAppointment.staffName,
                    type: 'consolidated_sale',
                    category: 'Consolidated Sale',
                    description: `Hair Cut for ${updatedAppointment.clientName}`,
                    amount: totalAmount,
                    paymentMethod: 'mobile_payment',
                    status: 'completed',
                    location: updatedAppointment.location,
                    source: 'calendar',
                    reference: {
                        type: 'appointment',
                        id: updatedAppointment.id
                    },
                    items: [serviceItem],
                    serviceAmount: serviceAmount,
                    productAmount: productAmount,
                    originalServiceAmount: originalServiceAmount,
                    discountPercentage: discountPercentage,
                    discountAmount: discountAmount,
                    metadata: {
                        appointmentId: updatedAppointment.id,
                        transactionType: 'consolidated',
                        serviceCount: 1,
                        productCount: 0,
                        discountApplied: discountPercentage && discountPercentage > 0,
                        originalTotal: originalServiceAmount + productAmount,
                        finalTotal: totalAmount
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                console.log('📊 Created Transaction:', transaction);

                // Step 5: Save to localStorage (simulates transaction provider)
                const existingTransactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]');
                existingTransactions.push(transaction);
                localStorage.setItem('vanity_transactions', JSON.stringify(existingTransactions));

                console.log('💾 Transaction saved to localStorage');

                document.getElementById('simulate-result').innerHTML = `
                    <div class="result success">
                        ✅ Appointment payment simulated successfully!
                        <br><strong>Transaction ID:</strong> ${transactionId}
                        <br><strong>Original Amount:</strong> QAR ${originalServiceAmount}
                        <br><strong>Discount:</strong> ${discountPercentage}% (QAR ${discountAmount})
                        <br><strong>Final Amount:</strong> QAR ${totalAmount}
                        <br><strong>Payment Method:</strong> ${paymentDialogParams.paymentMethod}
                        <br><br>✅ Transaction saved to localStorage with correct discount structure.
                    </div>
                `;

            } catch (error) {
                console.error('❌ Simulation error:', error);
                document.getElementById('simulate-result').innerHTML = `
                    <div class="result error">❌ Simulation failed: ${error.message}</div>
                `;
            }
        }

        function verifyTransactionData() {
            try {
                const stored = localStorage.getItem('vanity_transactions');
                if (!stored) {
                    document.getElementById('verify-result').innerHTML = '<div class="result error">No transactions found. Run simulation first.</div>';
                    return;
                }

                const transactions = JSON.parse(stored);
                if (transactions.length === 0) {
                    document.getElementById('verify-result').innerHTML = '<div class="result error">No transactions in storage.</div>';
                    return;
                }

                const latestTransaction = transactions[transactions.length - 1];
                console.log('🔍 Verifying transaction:', latestTransaction);

                // Verification checks
                const checks = {
                    hasCorrectAmount: latestTransaction.amount === 90,
                    hasDiscountMetadata: latestTransaction.metadata?.discountApplied === true,
                    hasCorrectOriginalTotal: latestTransaction.metadata?.originalTotal === 100,
                    hasCorrectFinalTotal: latestTransaction.metadata?.finalTotal === 90,
                    hasCorrectDiscountPercentage: latestTransaction.discountPercentage === 10,
                    hasCorrectDiscountAmount: latestTransaction.discountAmount === 10,
                    hasCorrectPaymentMethod: latestTransaction.paymentMethod === 'mobile_payment',
                    hasCorrectServiceAmount: latestTransaction.serviceAmount === 90,
                    hasCorrectSource: latestTransaction.source === 'calendar'
                };

                const passedChecks = Object.values(checks).filter(Boolean).length;
                const totalChecks = Object.keys(checks).length;
                const allPassed = passedChecks === totalChecks;

                let html = `
                    <div class="result ${allPassed ? 'success' : 'error'}">
                        <h4>${allPassed ? '✅' : '❌'} Transaction Verification (${passedChecks}/${totalChecks} passed)</h4>
                        <ul>
                `;

                Object.entries(checks).forEach(([check, passed]) => {
                    const icon = passed ? '✅' : '❌';
                    const checkName = check.replace(/([A-Z])/g, ' $1').toLowerCase();
                    html += `<li>${icon} ${checkName}</li>`;
                });

                html += `
                        </ul>
                        <h4>📋 Transaction Data:</h4>
                        <pre>${JSON.stringify(latestTransaction, null, 2)}</pre>
                    </div>
                `;

                document.getElementById('verify-result').innerHTML = html;

            } catch (error) {
                document.getElementById('verify-result').innerHTML = `
                    <div class="result error">❌ Verification failed: ${error.message}</div>
                `;
            }
        }

        function testAccountingDisplay() {
            try {
                const stored = localStorage.getItem('vanity_transactions');
                if (!stored) {
                    document.getElementById('display-result').innerHTML = '<div class="result error">No transactions found. Run simulation first.</div>';
                    return;
                }

                const transactions = JSON.parse(stored);
                const latestTransaction = transactions[transactions.length - 1];

                // Simulate the accounting page display logic
                const hasDiscount = latestTransaction.metadata?.discountApplied;
                
                let displayHtml = '';
                if (hasDiscount) {
                    displayHtml = `
                        <div style="display: flex; flex-direction: column;">
                            <div style="font-size: 14px; color: #666; text-decoration: line-through;">
                                QAR ${latestTransaction.metadata.originalTotal.toFixed(2)}
                            </div>
                            <div style="font-weight: bold; color: green;">
                                QAR ${latestTransaction.amount.toFixed(2)}
                            </div>
                            <div style="font-size: 12px; color: green;">
                                ${latestTransaction.discountPercentage}% off
                            </div>
                        </div>
                    `;
                } else {
                    displayHtml = `
                        <div style="font-weight: bold;">
                            QAR ${latestTransaction.amount.toFixed(2)}
                        </div>
                    `;
                }

                document.getElementById('display-result').innerHTML = `
                    <div class="result ${hasDiscount ? 'success' : 'error'}">
                        <h4>🖥️ How Accounting Page Should Display This Transaction:</h4>
                        <div style="border: 1px solid #ddd; padding: 10px; background: white; margin: 10px 0;">
                            ${displayHtml}
                        </div>
                        <p><strong>Display Logic Used:</strong> ${hasDiscount ? 'Discount display (crossed out original + green final)' : 'Regular display (no discount detected)'}</p>
                        <p><strong>Metadata.discountApplied:</strong> ${latestTransaction.metadata?.discountApplied}</p>
                        <p><strong>Expected Result:</strong> Should show QAR 100.00 crossed out and QAR 90.00 in green with "10% off"</p>
                        
                        ${hasDiscount ? 
                            '<p style="color: green;">✅ <strong>This should display correctly in the accounting page!</strong></p>' :
                            '<p style="color: red;">❌ <strong>This will NOT display the discount correctly. Check metadata.discountApplied flag.</strong></p>'
                        }
                    </div>
                `;

            } catch (error) {
                document.getElementById('display-result').innerHTML = `
                    <div class="result error">❌ Display test failed: ${error.message}</div>
                `;
            }
        }

        // Auto-run on page load
        window.onload = () => {
            console.log('🧪 Appointment Discount Test Tool Loaded');
        };
    </script>
</body>
</html>
